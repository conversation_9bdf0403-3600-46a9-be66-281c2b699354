"""
贝叶斯概率计算模块

该模块提供了贝叶斯概率计算的各种功能，包括：
1. 基本贝叶斯定理计算
2. 朴素贝叶斯分类器
3. 贝叶斯网络基础实现
4. 实用示例和工具函数

作者: AI Assistant
日期: 2024
"""

import numpy as np
from typing import Dict, List, Tuple, Any
from collections import defaultdict
import math


class BayesianCalculator:
    """贝叶斯概率计算器"""
    
    @staticmethod
    def bayes_theorem(prior: float, likelihood: float, evidence: float) -> float:
        """
        计算贝叶斯定理: P(A|B) = P(B|A) * P(A) / P(B)
        
        Args:
            prior: 先验概率 P(A)
            likelihood: 似然 P(B|A)
            evidence: 证据 P(B)
            
        Returns:
            后验概率 P(A|B)
        """
        if evidence == 0:
            raise ValueError("证据概率不能为0")
        return (likelihood * prior) / evidence
    
    @staticmethod
    def calculate_evidence(hypotheses: List[str], priors: Dict[str, float], 
                          likelihoods: Dict[str, float]) -> float:
        """
        计算全概率（证据）: P(B) = Σ P(B|Ai) * P(Ai)
        
        Args:
            hypotheses: 假设列表
            priors: 先验概率字典
            likelihoods: 似然字典
            
        Returns:
            证据概率
        """
        evidence = 0
        for hypothesis in hypotheses:
            evidence += likelihoods[hypothesis] * priors[hypothesis]
        return evidence
    
    @staticmethod
    def posterior_probabilities(hypotheses: List[str], priors: Dict[str, float],
                               likelihoods: Dict[str, float]) -> Dict[str, float]:
        """
        计算所有假设的后验概率
        
        Args:
            hypotheses: 假设列表
            priors: 先验概率字典
            likelihoods: 似然字典
            
        Returns:
            后验概率字典
        """
        evidence = BayesianCalculator.calculate_evidence(hypotheses, priors, likelihoods)
        posteriors = {}
        
        for hypothesis in hypotheses:
            posteriors[hypothesis] = BayesianCalculator.bayes_theorem(
                priors[hypothesis], likelihoods[hypothesis], evidence
            )
        
        return posteriors


class NaiveBayesClassifier:
    """朴素贝叶斯分类器"""
    
    def __init__(self):
        self.class_priors = {}
        self.feature_probs = defaultdict(lambda: defaultdict(dict))
        self.classes = set()
        self.features = set()
    
    def fit(self, X: List[List[Any]], y: List[str]):
        """
        训练朴素贝叶斯分类器
        
        Args:
            X: 特征矩阵
            y: 标签列表
        """
        n_samples = len(X)
        self.classes = set(y)
        
        # 计算类别先验概率
        class_counts = defaultdict(int)
        for label in y:
            class_counts[label] += 1
        
        for class_label in self.classes:
            self.class_priors[class_label] = class_counts[class_label] / n_samples
        
        # 计算特征条件概率
        for i, sample in enumerate(X):
            class_label = y[i]
            for j, feature_value in enumerate(sample):
                feature_name = f"feature_{j}"
                self.features.add(feature_name)
                
                if feature_value not in self.feature_probs[class_label][feature_name]:
                    self.feature_probs[class_label][feature_name][feature_value] = 0
                self.feature_probs[class_label][feature_name][feature_value] += 1
        
        # 归一化为概率
        for class_label in self.classes:
            class_count = class_counts[class_label]
            for feature_name in self.feature_probs[class_label]:
                for feature_value in self.feature_probs[class_label][feature_name]:
                    self.feature_probs[class_label][feature_name][feature_value] /= class_count
    
    def predict_proba(self, X: List[Any]) -> Dict[str, float]:
        """
        预测样本属于各类别的概率
        
        Args:
            X: 单个样本的特征
            
        Returns:
            各类别的概率字典
        """
        class_scores = {}
        
        for class_label in self.classes:
            # 开始时使用先验概率
            score = math.log(self.class_priors[class_label])
            
            # 乘以各特征的条件概率（在对数空间中相加）
            for j, feature_value in enumerate(X):
                feature_name = f"feature_{j}"
                if (feature_value in self.feature_probs[class_label][feature_name]):
                    prob = self.feature_probs[class_label][feature_name][feature_value]
                    if prob > 0:
                        score += math.log(prob)
                    else:
                        score += math.log(1e-10)  # 平滑处理
                else:
                    score += math.log(1e-10)  # 平滑处理
            
            class_scores[class_label] = score
        
        # 转换回概率空间并归一化
        max_score = max(class_scores.values())
        for class_label in class_scores:
            class_scores[class_label] = math.exp(class_scores[class_label] - max_score)
        
        total = sum(class_scores.values())
        for class_label in class_scores:
            class_scores[class_label] /= total
        
        return class_scores
    
    def predict(self, X: List[Any]) -> str:
        """
        预测样本的类别
        
        Args:
            X: 单个样本的特征
            
        Returns:
            预测的类别
        """
        probs = self.predict_proba(X)
        return max(probs, key=probs.get)


class BayesianNetwork:
    """简单的贝叶斯网络实现"""
    
    def __init__(self):
        self.nodes = {}
        self.edges = defaultdict(list)
        self.conditional_probs = {}
    
    def add_node(self, name: str, states: List[str]):
        """添加节点"""
        self.nodes[name] = states
    
    def add_edge(self, parent: str, child: str):
        """添加边（父节点到子节点）"""
        self.edges[parent].append(child)
    
    def set_conditional_probability(self, node: str, parents_state: Dict[str, str], 
                                   node_state: str, probability: float):
        """设置条件概率表"""
        key = (node, tuple(sorted(parents_state.items())), node_state)
        self.conditional_probs[key] = probability
    
    def get_probability(self, node: str, node_state: str, evidence: Dict[str, str] = None) -> float:
        """获取节点在给定证据下的概率"""
        if evidence is None:
            evidence = {}
        
        # 简化实现：直接查找条件概率表
        parents_evidence = {k: v for k, v in evidence.items() if k in self.get_parents(node)}
        key = (node, tuple(sorted(parents_evidence.items())), node_state)
        
        return self.conditional_probs.get(key, 0.0)
    
    def get_parents(self, node: str) -> List[str]:
        """获取节点的父节点"""
        parents = []
        for parent, children in self.edges.items():
            if node in children:
                parents.append(parent)
        return parents


def medical_diagnosis_example():
    """医疗诊断示例"""
    print("=== 医疗诊断贝叶斯示例 ===")
    
    # 假设：患有疾病D的先验概率为0.01
    # 测试T的准确率：如果有病，测试阳性概率为0.95；如果没病，测试阳性概率为0.05
    
    prior_disease = 0.01  # P(D)
    prior_no_disease = 0.99  # P(¬D)
    
    likelihood_positive_given_disease = 0.95  # P(T+|D)
    likelihood_positive_given_no_disease = 0.05  # P(T+|¬D)
    
    # 计算测试阳性的全概率
    evidence_positive = (likelihood_positive_given_disease * prior_disease + 
                        likelihood_positive_given_no_disease * prior_no_disease)
    
    # 计算测试阳性时患病的后验概率
    posterior_disease_given_positive = BayesianCalculator.bayes_theorem(
        prior_disease, likelihood_positive_given_disease, evidence_positive
    )
    
    print(f"疾病先验概率: {prior_disease:.3f}")
    print(f"测试阳性的概率: {evidence_positive:.3f}")
    print(f"测试阳性时患病的后验概率: {posterior_disease_given_positive:.3f}")
    print()


def spam_classification_example():
    """垃圾邮件分类示例"""
    print("=== 垃圾邮件分类示例 ===")
    
    # 训练数据：[是否包含"免费", 是否包含"点击", 是否包含"赢取"]
    X_train = [
        [1, 1, 1],  # 垃圾邮件
        [1, 0, 1],  # 垃圾邮件
        [0, 1, 1],  # 垃圾邮件
        [0, 0, 0],  # 正常邮件
        [0, 0, 1],  # 正常邮件
        [1, 0, 0],  # 正常邮件
    ]
    
    y_train = ["spam", "spam", "spam", "normal", "normal", "normal"]
    
    # 训练分类器
    classifier = NaiveBayesClassifier()
    classifier.fit(X_train, y_train)
    
    # 测试新邮件
    test_email = [1, 1, 0]  # 包含"免费"和"点击"，不包含"赢取"
    
    probabilities = classifier.predict_proba(test_email)
    prediction = classifier.predict(test_email)
    
    print("特征: [免费, 点击, 赢取]")
    print(f"测试邮件: {test_email}")
    print(f"预测概率: {probabilities}")
    print(f"预测结果: {prediction}")
    print()


if __name__ == "__main__":
    # 运行示例
    medical_diagnosis_example()
    spam_classification_example()
    
    print("=== 基本贝叶斯计算示例 ===")
    # 基本贝叶斯计算
    calc = BayesianCalculator()
    
    # 示例：天气预测
    hypotheses = ["晴天", "雨天"]
    priors = {"晴天": 0.7, "雨天": 0.3}
    likelihoods = {"晴天": 0.1, "雨天": 0.8}  # 观察到乌云的似然
    
    posteriors = calc.posterior_probabilities(hypotheses, priors, likelihoods)
    
    print("观察到乌云后的天气预测:")
    for weather, prob in posteriors.items():
        print(f"{weather}: {prob:.3f}")
