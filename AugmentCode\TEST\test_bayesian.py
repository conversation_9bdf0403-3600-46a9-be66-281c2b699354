"""
贝叶斯概率计算模块的测试文件

该文件包含了对bayesian_probability.py模块的全面测试
"""

import unittest
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bayesian_probability import BayesianCalculator, NaiveBayesClassifier, BayesianNetwork


class TestBayesianCalculator(unittest.TestCase):
    """测试贝叶斯计算器"""
    
    def setUp(self):
        self.calc = BayesianCalculator()
    
    def test_bayes_theorem(self):
        """测试贝叶斯定理计算"""
        # 经典的医疗测试例子
        prior = 0.01  # 疾病先验概率
        likelihood = 0.95  # 测试准确率
        evidence = 0.0595  # 测试阳性概率
        
        posterior = self.calc.bayes_theorem(prior, likelihood, evidence)
        expected = (0.95 * 0.01) / 0.0595
        
        self.assertAlmostEqual(posterior, expected, places=6)
    
    def test_evidence_calculation(self):
        """测试证据概率计算"""
        hypotheses = ["A", "B"]
        priors = {"A": 0.6, "B": 0.4}
        likelihoods = {"A": 0.8, "B": 0.3}
        
        evidence = self.calc.calculate_evidence(hypotheses, priors, likelihoods)
        expected = 0.8 * 0.6 + 0.3 * 0.4
        
        self.assertAlmostEqual(evidence, expected, places=6)
    
    def test_posterior_probabilities(self):
        """测试后验概率计算"""
        hypotheses = ["H1", "H2"]
        priors = {"H1": 0.7, "H2": 0.3}
        likelihoods = {"H1": 0.2, "H2": 0.9}
        
        posteriors = self.calc.posterior_probabilities(hypotheses, priors, likelihoods)
        
        # 检查概率和为1
        total_prob = sum(posteriors.values())
        self.assertAlmostEqual(total_prob, 1.0, places=6)
        
        # 检查具体值
        evidence = 0.2 * 0.7 + 0.9 * 0.3
        expected_h1 = (0.2 * 0.7) / evidence
        expected_h2 = (0.9 * 0.3) / evidence
        
        self.assertAlmostEqual(posteriors["H1"], expected_h1, places=6)
        self.assertAlmostEqual(posteriors["H2"], expected_h2, places=6)


class TestNaiveBayesClassifier(unittest.TestCase):
    """测试朴素贝叶斯分类器"""
    
    def setUp(self):
        self.classifier = NaiveBayesClassifier()
        
        # 简单的训练数据
        self.X_train = [
            [1, 0],  # 类别A
            [1, 1],  # 类别A
            [0, 1],  # 类别B
            [0, 0],  # 类别B
        ]
        self.y_train = ["A", "A", "B", "B"]
        
        self.classifier.fit(self.X_train, self.y_train)
    
    def test_fit(self):
        """测试训练过程"""
        # 检查类别先验概率
        self.assertAlmostEqual(self.classifier.class_priors["A"], 0.5, places=6)
        self.assertAlmostEqual(self.classifier.class_priors["B"], 0.5, places=6)
        
        # 检查特征条件概率
        self.assertEqual(len(self.classifier.classes), 2)
        self.assertIn("A", self.classifier.classes)
        self.assertIn("B", self.classifier.classes)
    
    def test_predict_proba(self):
        """测试概率预测"""
        test_sample = [1, 0]
        probs = self.classifier.predict_proba(test_sample)
        
        # 检查概率和为1
        total_prob = sum(probs.values())
        self.assertAlmostEqual(total_prob, 1.0, places=6)
        
        # 检查返回的类别
        self.assertIn("A", probs)
        self.assertIn("B", probs)
    
    def test_predict(self):
        """测试类别预测"""
        test_sample = [1, 1]
        prediction = self.classifier.predict(test_sample)
        
        # 应该返回一个有效的类别
        self.assertIn(prediction, ["A", "B"])


class TestBayesianNetwork(unittest.TestCase):
    """测试贝叶斯网络"""
    
    def setUp(self):
        self.network = BayesianNetwork()
        
        # 创建简单的网络：天气 -> 洒水器 -> 草湿
        self.network.add_node("天气", ["晴", "雨"])
        self.network.add_node("洒水器", ["开", "关"])
        self.network.add_node("草湿", ["湿", "干"])
        
        self.network.add_edge("天气", "洒水器")
        self.network.add_edge("天气", "草湿")
        self.network.add_edge("洒水器", "草湿")
    
    def test_add_node(self):
        """测试添加节点"""
        self.assertIn("天气", self.network.nodes)
        self.assertIn("洒水器", self.network.nodes)
        self.assertIn("草湿", self.network.nodes)
        
        self.assertEqual(self.network.nodes["天气"], ["晴", "雨"])
    
    def test_add_edge(self):
        """测试添加边"""
        self.assertIn("洒水器", self.network.edges["天气"])
        self.assertIn("草湿", self.network.edges["天气"])
        self.assertIn("草湿", self.network.edges["洒水器"])
    
    def test_get_parents(self):
        """测试获取父节点"""
        parents_sprinkler = self.network.get_parents("洒水器")
        parents_grass = self.network.get_parents("草湿")
        
        self.assertEqual(parents_sprinkler, ["天气"])
        self.assertIn("天气", parents_grass)
        self.assertIn("洒水器", parents_grass)


def run_comprehensive_test():
    """运行综合测试"""
    print("=== 运行贝叶斯概率计算模块测试 ===\n")
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(TestBayesianCalculator))
    test_suite.addTest(unittest.makeSuite(TestNaiveBayesClassifier))
    test_suite.addTest(unittest.makeSuite(TestBayesianNetwork))
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n✅ 所有测试通过！")
    else:
        print(f"\n❌ 测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
    
    return result.wasSuccessful()


def demo_advanced_examples():
    """演示高级示例"""
    print("\n=== 高级贝叶斯应用示例 ===\n")
    
    # 1. 多假设贝叶斯推理
    print("1. 多假设疾病诊断:")
    calc = BayesianCalculator()
    
    diseases = ["感冒", "流感", "COVID-19"]
    priors = {"感冒": 0.6, "流感": 0.3, "COVID-19": 0.1}
    
    # 观察到发烧症状的似然
    fever_likelihoods = {"感冒": 0.3, "流感": 0.8, "COVID-19": 0.9}
    
    posteriors = calc.posterior_probabilities(diseases, priors, fever_likelihoods)
    
    print("观察到发烧症状后的疾病概率:")
    for disease, prob in posteriors.items():
        print(f"  {disease}: {prob:.3f}")
    
    # 2. 文本分类示例
    print("\n2. 情感分析分类:")
    
    # 训练数据：[包含积极词汇数, 包含消极词汇数, 句子长度类别(0=短,1=长)]
    X_sentiment = [
        [3, 0, 1],  # 积极
        [2, 0, 0],  # 积极
        [1, 0, 1],  # 积极
        [0, 3, 1],  # 消极
        [0, 2, 0],  # 消极
        [0, 1, 1],  # 消极
        [1, 1, 0],  # 中性
        [1, 1, 1],  # 中性
    ]
    
    y_sentiment = ["积极", "积极", "积极", "消极", "消极", "消极", "中性", "中性"]
    
    sentiment_classifier = NaiveBayesClassifier()
    sentiment_classifier.fit(X_sentiment, y_sentiment)
    
    # 测试新句子
    test_sentences = [
        [2, 0, 1],  # 2个积极词，0个消极词，长句子
        [0, 2, 0],  # 0个积极词，2个消极词，短句子
        [1, 1, 1],  # 1个积极词，1个消极词，长句子
    ]
    
    for i, sentence in enumerate(test_sentences):
        probs = sentiment_classifier.predict_proba(sentence)
        prediction = sentiment_classifier.predict(sentence)
        print(f"  句子{i+1} {sentence}: 预测={prediction}, 概率={probs}")


if __name__ == "__main__":
    # 运行测试
    success = run_comprehensive_test()
    
    if success:
        # 运行演示
        demo_advanced_examples()
    
    print("\n=== 测试完成 ===")
